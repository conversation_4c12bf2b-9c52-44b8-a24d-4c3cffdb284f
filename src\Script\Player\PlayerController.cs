/*
 * 玩家控制器 - 等距视角移动系统
 */

using Godot;
using ArchipelagoGame.Interfaces;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家控制器 - 实现等距视角的角色移动
    /// 继承自CharacterBody2D以获得物理移动和碰撞检测功能
    /// </summary>
    public partial class PlayerController : CharacterBody2D
    {

        [Export] public float Speed = 300.0f; // 移动速度（像素/秒）
        public Node2D Player;
        public AnimationPlayer PlayerAnimation;
        public override void _Ready()
        {

            Player = GetNode<Node2D>("/root/PlayerBehavior/Player");
            PlayerAnimation = GetNode<AnimationPlayer>("/root/PlayerBehavior/Player/AnimationPlayer");
        }


        public override void _PhysicsProcess(double delta)
        {
            // 1. 获取输入方向（-1到1之间的值）
            Vector2 inputDirection = Input.GetVector("ui_left", "ui_right", "ui_up", "ui_down");
            Velocity = inputDirection * Speed;
            MoveAndSlide();

            // 2. 根据移动方向播放相应动画
            if (inputDirection != Vector2.Zero)
            {
                string animationName = GetDirectionAnimation(inputDirection);

                // 只有当动画改变时才播放新动画，避免重复播放
                if (PlayerAnimation.CurrentAnimation != animationName)
                {
                    PlayerAnimation.Play(animationName);
                }
            }
            else
            {
                // 停止移动时播放静止动画或停止当前动画
                if (PlayerAnimation.IsPlaying())
                {
                    PlayerAnimation.Stop();
                    // 或者播放静止动画: PlayerAnimation.Play("idle");
                }
            }
        }
    }
        
}
